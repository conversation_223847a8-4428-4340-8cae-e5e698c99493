<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>傅里叶变换、DFT、DCT原理详解</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            }
        };
    </script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        .formula-box {
            background-color: #ecf0f1;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
        }
        .explanation {
            background-color: #e8f6f3;
            border-left: 4px solid #1abc9c;
            padding: 15px;
            margin: 15px 0;
        }
        .example {
            background-color: #fef9e7;
            border: 1px solid #f39c12;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .canvas-container {
            text-align: center;
            margin: 20px 0;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .step {
            counter-increment: step-counter;
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .step::before {
            content: "步骤 " counter(step-counter) ": ";
            font-weight: bold;
            color: #e74c3c;
            font-size: 1.1em;
        }
        body {
            counter-reset: step-counter;
        }
        .interactive-demo {
            background-color: #f8f9fa;
            border: 2px solid #6c757d;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>傅里叶变换、DFT、DCT原理详解</h1>
        
        <h2>1. 傅里叶级数的数学推导</h2>

        <div class="step">
            <h3>步骤1：周期函数的正交基展开</h3>
            <p>设 $f(t)$ 是周期为 $T$ 的函数，我们要将其展开为三角函数级数：</p>

            <div class="formula-box">
                $$f(t) = \frac{a_0}{2} + \sum_{n=1}^{\infty} \left[ a_n \cos\left(\frac{2\pi n t}{T}\right) + b_n \sin\left(\frac{2\pi n t}{T}\right) \right]$$
            </div>
        </div>

        <div class="step">
            <h3>步骤2：利用正交性求系数</h3>
            <p>三角函数的正交性：</p>
            <div class="formula-box">
                $$\int_{-T/2}^{T/2} \cos\left(\frac{2\pi m t}{T}\right) \cos\left(\frac{2\pi n t}{T}\right) dt = \begin{cases}
                0 & \text{if } m \neq n \\
                T/2 & \text{if } m = n \neq 0 \\
                T & \text{if } m = n = 0
                \end{cases}$$
            </div>

            <p>利用正交性，两边同时乘以 $\cos\left(\frac{2\pi k t}{T}\right)$ 并积分：</p>
            <div class="formula-box">
                $$\int_{-T/2}^{T/2} f(t) \cos\left(\frac{2\pi k t}{T}\right) dt = a_k \cdot \frac{T}{2}$$
            </div>

            <p>因此：</p>
            <div class="formula-box">
                $$a_k = \frac{2}{T} \int_{-T/2}^{T/2} f(t) \cos\left(\frac{2\pi k t}{T}\right) dt$$
                $$b_k = \frac{2}{T} \int_{-T/2}^{T/2} f(t) \sin\left(\frac{2\pi k t}{T}\right) dt$$
            </div>
        </div>

        <div class="step">
            <h3>步骤3：复指数形式推导</h3>
            <p>利用欧拉公式：$e^{j\theta} = \cos\theta + j\sin\theta$</p>
            <div class="formula-box">
                $$\cos\left(\frac{2\pi n t}{T}\right) = \frac{1}{2}\left[e^{j\frac{2\pi n t}{T}} + e^{-j\frac{2\pi n t}{T}}\right]$$
                $$\sin\left(\frac{2\pi n t}{T}\right) = \frac{1}{2j}\left[e^{j\frac{2\pi n t}{T}} - e^{-j\frac{2\pi n t}{T}}\right]$$
            </div>

            <p>代入傅里叶级数：</p>
            <div class="formula-box">
                $$f(t) = \sum_{n=-\infty}^{\infty} c_n e^{j\frac{2\pi n t}{T}}$$
            </div>

            <p>其中复系数：</p>
            <div class="formula-box">
                $$c_n = \frac{1}{T} \int_{-T/2}^{T/2} f(t) e^{-j\frac{2\pi n t}{T}} dt$$
            </div>
        </div>

        <div class="step">
            <h3>步骤4：从傅里叶级数到傅里叶变换</h3>
            <p>当 $T \to \infty$ 时，离散频率 $\omega_n = \frac{2\pi n}{T}$ 变为连续频率 $\omega$：</p>

            <div class="formula-box">
                $$\Delta\omega = \frac{2\pi}{T}, \quad T c_n \to F(\omega)$$
            </div>

            <p>极限过程：</p>
            <div class="formula-box">
                $$\lim_{T \to \infty} \sum_{n=-\infty}^{\infty} c_n e^{j\omega_n t} \Delta\omega = \frac{1}{2\pi} \int_{-\infty}^{\infty} F(\omega) e^{j\omega t} d\omega$$
            </div>

            <p>得到傅里叶变换对：</p>
            <div class="formula-box">
                $$F(\omega) = \int_{-\infty}^{\infty} f(t) e^{-j\omega t} dt \quad \text{(正变换)}$$
                $$f(t) = \frac{1}{2\pi} \int_{-\infty}^{\infty} F(\omega) e^{j\omega t} d\omega \quad \text{(逆变换)}$$
            </div>
        </div>

        <h2>2. 离散傅里叶变换（DFT）的推导</h2>

        <div class="step">
            <h3>步骤1：从连续到离散的采样</h3>
            <p>设连续信号 $f(t)$ 以采样间隔 $\Delta t$ 进行采样：</p>
            <div class="formula-box">
                $$x[n] = f(n\Delta t), \quad n = 0, 1, 2, ..., N-1$$
            </div>

            <p>采样频率：$f_s = \frac{1}{\Delta t}$，总时长：$T = N\Delta t$</p>
        </div>

        <div class="step">
            <h3>步骤2：离散化傅里叶变换积分</h3>
            <p>将连续傅里叶变换的积分用矩形法则近似：</p>
            <div class="formula-box">
                $$F(\omega) = \int_{-\infty}^{\infty} f(t) e^{-j\omega t} dt \approx \sum_{n=0}^{N-1} f(n\Delta t) e^{-j\omega n\Delta t} \Delta t$$
            </div>

            <p>频率也需要离散化。设 $\omega_k = \frac{2\pi k}{N\Delta t} = \frac{2\pi k}{T}$，其中 $k = 0, 1, ..., N-1$</p>
        </div>

        <div class="step">
            <h3>步骤3：推导DFT公式</h3>
            <p>代入离散频率：</p>
            <div class="formula-box">
                $$F(\omega_k) \approx \Delta t \sum_{n=0}^{N-1} x[n] e^{-j\frac{2\pi kn}{N}}$$
            </div>

            <p>定义DFT系数 $X[k] = \frac{F(\omega_k)}{\Delta t}$：</p>
            <div class="formula-box">
                $$X[k] = \sum_{n=0}^{N-1} x[n] e^{-j\frac{2\pi kn}{N}} = \sum_{n=0}^{N-1} x[n] W_N^{kn}$$
            </div>

            <p>其中 $W_N = e^{-j\frac{2\pi}{N}}$ 称为旋转因子。</p>
        </div>

        <div class="step">
            <h3>步骤4：DFT的矩阵表示推导</h3>
            <p>DFT可以写成矩阵形式：</p>
            <div class="formula-box">
                $$\begin{bmatrix} X[0] \\ X[1] \\ \vdots \\ X[N-1] \end{bmatrix} =
                \begin{bmatrix}
                1 & 1 & 1 & \cdots & 1 \\
                1 & W_N & W_N^2 & \cdots & W_N^{N-1} \\
                1 & W_N^2 & W_N^4 & \cdots & W_N^{2(N-1)} \\
                \vdots & \vdots & \vdots & \ddots & \vdots \\
                1 & W_N^{N-1} & W_N^{2(N-1)} & \cdots & W_N^{(N-1)^2}
                \end{bmatrix}
                \begin{bmatrix} x[0] \\ x[1] \\ \vdots \\ x[N-1] \end{bmatrix}$$
            </div>

            <p>DFT矩阵的第 $(k,n)$ 元素为：$W_N^{kn} = e^{-j\frac{2\pi kn}{N}}$</p>
        </div>

        <div class="step">
            <h3>步骤5：逆DFT推导</h3>
            <p>DFT矩阵是酉矩阵，其逆矩阵为：</p>
            <div class="formula-box">
                $$x[n] = \frac{1}{N} \sum_{k=0}^{N-1} X[k] e^{j\frac{2\pi kn}{N}} = \frac{1}{N} \sum_{k=0}^{N-1} X[k] W_N^{-kn}$$
            </div>

            <p>证明：利用几何级数求和公式</p>
            <div class="formula-box">
                $$\sum_{k=0}^{N-1} W_N^{k(n-m)} = \begin{cases}
                N & \text{if } n = m \\
                0 & \text{if } n \neq m
                \end{cases}$$
            </div>
        </div>

        <h2>3. 离散余弦变换（DCT）的完整推导</h2>

        <div class="step">
            <h3>步骤1：从DFT到DCT的动机</h3>
            <p>DFT假设信号是周期的，但实际信号通常不是周期的，这会在边界产生不连续性。</p>
            <p>DCT通过对称扩展避免边界不连续：</p>
            <div class="formula-box">
                $$\tilde{x}[n] = \begin{cases}
                x[n] & 0 \leq n \leq N-1 \\
                x[2N-1-n] & N \leq n \leq 2N-1
                \end{cases}$$
            </div>
        </div>

        <div class="step">
            <h3>步骤2：对称扩展信号的DFT</h3>
            <p>对长度为 $2N$ 的对称信号 $\tilde{x}[n]$ 进行DFT：</p>
            <div class="formula-box">
                $$\tilde{X}[k] = \sum_{n=0}^{2N-1} \tilde{x}[n] e^{-j\frac{\pi kn}{N}}$$
            </div>

            <p>利用对称性：</p>
            <div class="formula-box">
                $$\tilde{X}[k] = \sum_{n=0}^{N-1} x[n] e^{-j\frac{\pi kn}{N}} + \sum_{n=N}^{2N-1} x[2N-1-n] e^{-j\frac{\pi kn}{N}}$$
            </div>

            <p>第二项变量替换 $m = 2N-1-n$：</p>
            <div class="formula-box">
                $$\tilde{X}[k] = \sum_{n=0}^{N-1} x[n] e^{-j\frac{\pi kn}{N}} + \sum_{m=0}^{N-1} x[m] e^{-j\frac{\pi k(2N-1-m)}{N}}$$
            </div>
        </div>

        <div class="step">
            <h3>步骤3：简化对称DFT</h3>
            <p>利用 $e^{-j\pi k(2N-1)} = e^{-j\pi k} \cdot e^{j\pi k} = (-1)^k$：</p>
            <div class="formula-box">
                $$\tilde{X}[k] = \sum_{n=0}^{N-1} x[n] \left[ e^{-j\frac{\pi kn}{N}} + (-1)^k e^{j\frac{\pi kn}{N}} \right]$$
            </div>

            <p>当 $k$ 为偶数时：</p>
            <div class="formula-box">
                $$\tilde{X}[k] = 2\sum_{n=0}^{N-1} x[n] \cos\left(\frac{\pi kn}{N}\right)$$
            </div>
        </div>

        <div class="step">
            <h3>步骤4：推导DCT-II公式</h3>
            <p>为了获得更好的频率分辨率，引入相位偏移 $\frac{\pi}{2N}$：</p>
            <div class="formula-box">
                $$X[k] = \sum_{n=0}^{N-1} x[n] \cos\left(\frac{\pi k (2n+1)}{2N}\right)$$
            </div>

            <p>加入归一化因子：</p>
            <div class="formula-box">
                $$X[k] = \alpha[k] \sum_{n=0}^{N-1} x[n] \cos\left(\frac{\pi k (2n+1)}{2N}\right)$$
            </div>

            <p>其中：</p>
            <div class="formula-box">
                $$\alpha[k] = \begin{cases}
                \sqrt{\frac{1}{N}} & k = 0 \\
                \sqrt{\frac{2}{N}} & k = 1, 2, ..., N-1
                \end{cases}$$
            </div>
        </div>

        <div class="step">
            <h3>步骤5：二维DCT推导</h3>
            <p>二维DCT是一维DCT的可分离扩展：</p>
            <div class="formula-box">
                $$F[u,v] = \alpha[u]\alpha[v] \sum_{x=0}^{N-1} \sum_{y=0}^{N-1} f[x,y] \cos\left(\frac{\pi u (2x+1)}{2N}\right) \cos\left(\frac{\pi v (2y+1)}{2N}\right)$$
            </div>

            <p>可分离性证明：设 $g[x,v] = \alpha[v] \sum_{y=0}^{N-1} f[x,y] \cos\left(\frac{\pi v (2y+1)}{2N}\right)$</p>
            <div class="formula-box">
                $$F[u,v] = \alpha[u] \sum_{x=0}^{N-1} g[x,v] \cos\left(\frac{\pi u (2x+1)}{2N}\right)$$
            </div>
        </div>

        <div class="step">
            <h3>步骤6：逆DCT推导</h3>
            <p>DCT基函数的正交性：</p>
            <div class="formula-box">
                $$\sum_{k=0}^{N-1} \alpha^2[k] \cos\left(\frac{\pi k (2m+1)}{2N}\right) \cos\left(\frac{\pi k (2n+1)}{2N}\right) = \delta_{mn}$$
            </div>

            <p>因此逆DCT为：</p>
            <div class="formula-box">
                $$x[n] = \sum_{k=0}^{N-1} \alpha[k] X[k] \cos\left(\frac{\pi k (2n+1)}{2N}\right)$$
            </div>
        </div>

        <h2>4. 图像DCT的数学分析</h2>

        <div class="step">
            <h3>步骤1：图像DCT的基函数分析</h3>
            <p>二维DCT的基函数为：</p>
            <div class="formula-box">
                $$\phi_{u,v}(x,y) = \alpha[u]\alpha[v] \cos\left(\frac{\pi u (2x+1)}{2N}\right) \cos\left(\frac{\pi v (2y+1)}{2N}\right)$$
            </div>

            <p>每个DCT系数表示图像在对应基函数上的投影：</p>
            <div class="formula-box">
                $$F[u,v] = \langle f, \phi_{u,v} \rangle = \sum_{x=0}^{N-1} \sum_{y=0}^{N-1} f[x,y] \phi_{u,v}(x,y)$$
            </div>
        </div>

        <div class="step">
            <h3>步骤2：频率域特性分析</h3>
            <p>DCT系数的频率解释：</p>
            <ul>
                <li>$F[0,0]$：DC分量（平均亮度）</li>
                <li>$F[0,v], F[u,0]$：水平/垂直低频分量</li>
                <li>$F[u,v]$ (大的$u,v$)：高频细节分量</li>
            </ul>

            <div class="formula-box">
                $$F[0,0] = \frac{1}{N} \sum_{x=0}^{N-1} \sum_{y=0}^{N-1} f[x,y] = \text{平均值}$$
            </div>
        </div>

        <div class="step">
            <h3>步骤3：能量压缩性质推导</h3>
            <p>Parseval定理在DCT中的形式：</p>
            <div class="formula-box">
                $$\sum_{x=0}^{N-1} \sum_{y=0}^{N-1} |f[x,y]|^2 = \sum_{u=0}^{N-1} \sum_{v=0}^{N-1} |F[u,v]|^2$$
            </div>

            <p>对于自然图像，能量主要集中在低频：</p>
            <div class="formula-box">
                $$\sum_{u=0}^{K} \sum_{v=0}^{K} |F[u,v]|^2 \gg \sum_{u=K+1}^{N-1} \sum_{v=K+1}^{N-1} |F[u,v]|^2$$
            </div>

            <p>其中 $K \ll N$，这是DCT压缩的理论基础。</p>
        </div>

        <div class="interactive-demo">
            <h3>交互式图像DCT演示</h3>
            <div class="canvas-container">
                <canvas id="originalCanvas" width="256" height="256"></canvas>
                <canvas id="dctCanvas" width="256" height="256"></canvas>
                <canvas id="reconstructedCanvas" width="256" height="256"></canvas>
            </div>
            <div style="text-align: center;">
                <p>原始图像 → DCT频谱 → 重构图像</p>
                <button onclick="generateTestImage()">生成测试图像</button>
                <button onclick="performDCT()">执行DCT</button>
                <button onclick="compressImage()">压缩图像</button>
                <br>
                <label>压缩比例: <input type="range" id="compressionSlider" min="0.1" max="1" step="0.1" value="0.5" onchange="updateCompression()"></label>
                <span id="compressionValue">50%</span>
            </div>
        </div>

        <div class="step">
            <h3>步骤4：量化理论推导</h3>
            <p>量化是有损压缩的关键步骤：</p>
            <div class="formula-box">
                $$F_q[u,v] = \text{round}\left(\frac{F[u,v]}{Q[u,v]}\right)$$
            </div>

            <p>量化误差分析：</p>
            <div class="formula-box">
                $$e_q[u,v] = F[u,v] - F_q[u,v] \cdot Q[u,v]$$
            </div>

            <p>均匀量化的误差范围：$|e_q[u,v]| \leq \frac{Q[u,v]}{2}$</p>

            <p>重构图像的总误差：</p>
            <div class="formula-box">
                $$E = \sum_{x=0}^{N-1} \sum_{y=0}^{N-1} |f[x,y] - \hat{f}[x,y]|^2 = \sum_{u=0}^{N-1} \sum_{v=0}^{N-1} |e_q[u,v]|^2$$
            </div>
        </div>

        <div class="step">
            <h3>步骤5：最优量化矩阵推导</h3>
            <p>基于人眼视觉特性的量化矩阵设计。人眼对不同频率的敏感度函数：</p>
            <div class="formula-box">
                $$S(u,v) = S_0 \cdot \exp\left(-\alpha \sqrt{u^2 + v^2}\right)$$
            </div>

            <p>最优量化步长：</p>
            <div class="formula-box">
                $$Q[u,v] = \frac{\sigma_F[u,v]}{S(u,v)} \cdot \beta$$
            </div>

            <p>其中 $\sigma_F[u,v]$ 是DCT系数的标准差，$\beta$ 是质量控制参数。</p>
        </div>

        <h2>5. 快速DCT算法推导</h2>

        <div class="step">
            <h3>步骤1：DCT与DFT的关系</h3>
            <p>一维DCT可以通过DFT计算。构造长度为 $2N$ 的序列：</p>
            <div class="formula-box">
                $$y[n] = \begin{cases}
                x[n] & 0 \leq n \leq N-1 \\
                0 & N \leq n \leq 2N-1
                \end{cases}$$
            </div>

            <p>预处理：</p>
            <div class="formula-box">
                $$z[n] = y[2n] + j \cdot y[2n+1], \quad n = 0, 1, ..., N-1$$
            </div>
        </div>

        <div class="step">
            <h3>步骤2：快速DCT的蝶形运算</h3>
            <p>利用DCT的对称性，可以将 $N$ 点DCT分解为两个 $N/2$ 点DCT：</p>
            <div class="formula-box">
                $$X[k] = X_e[k] + W_k \cdot X_o[k], \quad k = 0, 1, ..., N/2-1$$
                $$X[k+N/2] = X_e[k] - W_k \cdot X_o[k]$$
            </div>

            <p>其中：</p>
            <div class="formula-box">
                $$W_k = \cos\left(\frac{\pi k}{2N}\right) - j\sin\left(\frac{\pi k}{2N}\right)$$
            </div>

            <p>复杂度从 $O(N^2)$ 降低到 $O(N\log N)$。</p>
        </div>

        <h2>6. 收敛性和误差分析</h2>

        <div class="step">
            <h3>步骤1：傅里叶级数的收敛性</h3>
            <p>Dirichlet条件：如果 $f(t)$ 满足：</p>
            <ul>
                <li>在一个周期内绝对可积：$\int_{-T/2}^{T/2} |f(t)| dt < \infty$</li>
                <li>有有限个不连续点</li>
                <li>有有限个极值点</li>
            </ul>

            <p>则傅里叶级数逐点收敛：</p>
            <div class="formula-box">
                $$\lim_{N \to \infty} \sum_{n=-N}^{N} c_n e^{j\frac{2\pi n t}{T}} = \frac{f(t^+) + f(t^-)}{2}$$
            </div>
        </div>

        <div class="step">
            <h3>步骤2：DFT的频谱泄漏分析</h3>
            <p>当信号频率不是采样频率的整数倍时，会产生频谱泄漏。设信号：</p>
            <div class="formula-box">
                $$f(t) = A\cos(2\pi f_0 t + \phi)$$
            </div>

            <p>采样后的DFT：</p>
            <div class="formula-box">
                $$X[k] = \frac{AN}{2} e^{j\phi} \frac{\sin(\pi(f_0 T - k))}{\sin(\pi(f_0 T - k)/N)} e^{-j\pi(f_0 T - k)(N-1)/N}$$
            </div>

            <p>当 $f_0 T \neq k$ 时，能量会泄漏到其他频率分量。</p>
        </div>

        <div class="step">
            <h3>步骤3：DCT的边界效应分析</h3>
            <p>DCT通过对称扩展避免了DFT的周期性假设。边界处的连续性：</p>
            <div class="formula-box">
                $$\lim_{x \to 0^-} \tilde{f}(x) = \lim_{x \to 0^+} \tilde{f}(x) = f(0)$$
            </div>

            <p>这消除了Gibbs现象，使DCT更适合图像处理。</p>
        </div>

        <div class="step">
            <h3>步骤4：压缩误差的数学分析</h3>
            <p>设保留前 $K$ 个最大的DCT系数，压缩误差为：</p>
            <div class="formula-box">
                $$E_K = \sum_{(u,v) \notin S_K} |F[u,v]|^2$$
            </div>

            <p>其中 $S_K$ 是前 $K$ 个最大系数的索引集合。</p>

            <p>对于马尔可夫过程建模的图像，误差衰减率：</p>
            <div class="formula-box">
                $$E_K \approx C \cdot \rho^K$$
            </div>

            <p>其中 $0 < \rho < 1$ 是相关系数，$C$ 是常数。</p>
        </div>

        <h2>7. 三种变换的数学比较</h2>

        <div class="step">
            <h3>核函数比较</h3>
            <div class="formula-box">
                <h4>傅里叶变换核：</h4>
                $$K_{FT}(\omega, t) = e^{-j\omega t} = \cos(\omega t) - j\sin(\omega t)$$

                <h4>DFT核：</h4>
                $$K_{DFT}(k, n) = e^{-j\frac{2\pi kn}{N}} = W_N^{kn}$$

                <h4>DCT核：</h4>
                $$K_{DCT}(k, n) = \alpha[k] \cos\left(\frac{\pi k (2n+1)}{2N}\right)$$
            </div>
        </div>

        <div class="step">
            <h3>正交性比较</h3>
            <p><strong>DFT正交性：</strong></p>
            <div class="formula-box">
                $$\sum_{n=0}^{N-1} W_N^{kn} W_N^{-ln} = N\delta_{kl}$$
            </div>

            <p><strong>DCT正交性：</strong></p>
            <div class="formula-box">
                $$\sum_{n=0}^{N-1} \alpha[k]\alpha[l] \cos\left(\frac{\pi k (2n+1)}{2N}\right) \cos\left(\frac{\pi l (2n+1)}{2N}\right) = \delta_{kl}$$
            </div>
        </div>

        <h2>6. 实际应用案例</h2>

        <div class="step">
            <h3>JPEG图像压缩</h3>
            <p>JPEG压缩的核心步骤：</p>
            <ol>
                <li><strong>颜色空间转换：</strong>RGB → YCbCr</li>
                <li><strong>分块：</strong>将图像分成8×8像素块</li>
                <li><strong>DCT变换：</strong>每个块进行2D-DCT</li>
                <li><strong>量化：</strong>除以量化矩阵并四舍五入</li>
                <li><strong>编码：</strong>霍夫曼编码或算术编码</li>
            </ol>
        </div>

        <div class="example">
            <h4>量化矩阵示例（亮度分量）：</h4>
            <pre style="font-family: monospace; background-color: white; padding: 10px;">
16  11  10  16  24  40  51  61
12  12  14  19  26  58  60  55
14  13  16  24  40  57  69  56
14  17  22  29  51  87  80  62
18  22  37  56  68 109 103  77
24  35  55  64  81 104 113  92
49  64  78  87 103 121 120 101
72  92  95  98 112 100 103  99
            </pre>
        </div>

        <script>
            // 获取canvas元素
            const originalCanvas = document.getElementById('originalCanvas');
            const dctCanvas = document.getElementById('dctCanvas');
            const reconstructedCanvas = document.getElementById('reconstructedCanvas');

            const originalCtx = originalCanvas.getContext('2d');
            const dctCtx = dctCanvas.getContext('2d');
            const reconstructedCtx = reconstructedCanvas.getContext('2d');

            let originalImageData = null;
            let dctCoefficients = null;
            
            // 生成测试图像
            function generateTestImage() {
                const width = 256;
                const height = 256;
                const imageData = originalCtx.createImageData(width, height);
                
                // 创建一个简单的测试图像（渐变 + 一些几何形状）
                for (let y = 0; y < height; y++) {
                    for (let x = 0; x < width; x++) {
                        const index = (y * width + x) * 4;
                        
                        // 基础渐变
                        let value = Math.floor((x + y) / 2);
                        
                        // 添加一些几何形状
                        const centerX = width / 2;
                        const centerY = height / 2;
                        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
                        
                        if (distance < 50) {
                            value = 255; // 白色圆形
                        } else if (x > 100 && x < 150 && y > 100 && y < 150) {
                            value = 128; // 灰色方块
                        }
                        
                        imageData.data[index] = value;     // R
                        imageData.data[index + 1] = value; // G
                        imageData.data[index + 2] = value; // B
                        imageData.data[index + 3] = 255;   // A
                    }
                }
                
                originalCtx.putImageData(imageData, 0, 0);
                originalImageData = imageData;
            }
            
            // 简化的DCT实现（8x8块）
            function dct2D(block) {
                const N = 8;
                const result = Array(N).fill().map(() => Array(N).fill(0));
                
                for (let u = 0; u < N; u++) {
                    for (let v = 0; v < N; v++) {
                        let sum = 0;
                        for (let x = 0; x < N; x++) {
                            for (let y = 0; y < N; y++) {
                                sum += block[x][y] * 
                                       Math.cos((2 * x + 1) * u * Math.PI / (2 * N)) *
                                       Math.cos((2 * y + 1) * v * Math.PI / (2 * N));
                            }
                        }
                        
                        const alpha_u = u === 0 ? Math.sqrt(1/N) : Math.sqrt(2/N);
                        const alpha_v = v === 0 ? Math.sqrt(1/N) : Math.sqrt(2/N);
                        
                        result[u][v] = alpha_u * alpha_v * sum;
                    }
                }
                
                return result;
            }
            
            // 反DCT
            function idct2D(dctBlock) {
                const N = 8;
                const result = Array(N).fill().map(() => Array(N).fill(0));
                
                for (let x = 0; x < N; x++) {
                    for (let y = 0; y < N; y++) {
                        let sum = 0;
                        for (let u = 0; u < N; u++) {
                            for (let v = 0; v < N; v++) {
                                const alpha_u = u === 0 ? Math.sqrt(1/N) : Math.sqrt(2/N);
                                const alpha_v = v === 0 ? Math.sqrt(1/N) : Math.sqrt(2/N);
                                
                                sum += alpha_u * alpha_v * dctBlock[u][v] *
                                       Math.cos((2 * x + 1) * u * Math.PI / (2 * N)) *
                                       Math.cos((2 * y + 1) * v * Math.PI / (2 * N));
                            }
                        }
                        result[x][y] = Math.round(sum);
                    }
                }
                
                return result;
            }
            
            // 执行DCT
            function performDCT() {
                if (!originalImageData) {
                    alert('请先生成测试图像');
                    return;
                }
                
                const width = originalCanvas.width;
                const height = originalCanvas.height;
                dctCoefficients = [];
                
                // 显示DCT频谱
                const dctImageData = dctCtx.createImageData(width, height);
                
                // 处理8x8块
                for (let blockY = 0; blockY < height; blockY += 8) {
                    for (let blockX = 0; blockX < width; blockX += 8) {
                        // 提取8x8块
                        const block = Array(8).fill().map(() => Array(8).fill(0));
                        for (let y = 0; y < 8; y++) {
                            for (let x = 0; x < 8; x++) {
                                const pixelIndex = ((blockY + y) * width + (blockX + x)) * 4;
                                block[y][x] = originalImageData.data[pixelIndex] - 128; // 中心化
                            }
                        }
                        
                        // 执行DCT
                        const dctBlock = dct2D(block);
                        dctCoefficients.push(dctBlock);
                        
                        // 可视化DCT系数
                        for (let y = 0; y < 8; y++) {
                            for (let x = 0; x < 8; x++) {
                                const pixelIndex = ((blockY + y) * width + (blockX + x)) * 4;
                                const value = Math.abs(dctBlock[y][x]);
                                const normalizedValue = Math.min(255, value * 2); // 放大显示
                                
                                dctImageData.data[pixelIndex] = normalizedValue;
                                dctImageData.data[pixelIndex + 1] = normalizedValue;
                                dctImageData.data[pixelIndex + 2] = normalizedValue;
                                dctImageData.data[pixelIndex + 3] = 255;
                            }
                        }
                    }
                }
                
                dctCtx.putImageData(dctImageData, 0, 0);
                
                // 重构图像
                reconstructImage(1.0); // 100%保留
            }
            
            // 重构图像
            function reconstructImage(keepRatio) {
                if (!dctCoefficients) return;
                
                const width = reconstructedCanvas.width;
                const height = reconstructedCanvas.height;
                const reconstructedImageData = reconstructedCtx.createImageData(width, height);
                
                let coeffIndex = 0;
                
                for (let blockY = 0; blockY < height; blockY += 8) {
                    for (let blockX = 0; blockX < width; blockX += 8) {
                        // 获取DCT系数并应用压缩
                        const dctBlock = JSON.parse(JSON.stringify(dctCoefficients[coeffIndex]));
                        
                        // 压缩：保留左上角的系数
                        const keepSize = Math.floor(8 * keepRatio);
                        for (let y = 0; y < 8; y++) {
                            for (let x = 0; x < 8; x++) {
                                if (x >= keepSize || y >= keepSize) {
                                    dctBlock[y][x] = 0;
                                }
                            }
                        }
                        
                        // 反DCT
                        const reconstructedBlock = idct2D(dctBlock);
                        
                        // 放回图像
                        for (let y = 0; y < 8; y++) {
                            for (let x = 0; x < 8; x++) {
                                const pixelIndex = ((blockY + y) * width + (blockX + x)) * 4;
                                const value = Math.max(0, Math.min(255, reconstructedBlock[y][x] + 128));
                                
                                reconstructedImageData.data[pixelIndex] = value;
                                reconstructedImageData.data[pixelIndex + 1] = value;
                                reconstructedImageData.data[pixelIndex + 2] = value;
                                reconstructedImageData.data[pixelIndex + 3] = 255;
                            }
                        }
                        
                        coeffIndex++;
                    }
                }
                
                reconstructedCtx.putImageData(reconstructedImageData, 0, 0);
            }
            
            // 压缩图像
            function compressImage() {
                const slider = document.getElementById('compressionSlider');
                const keepRatio = parseFloat(slider.value);
                reconstructImage(keepRatio);
            }
            
            // 更新压缩比例
            function updateCompression() {
                const slider = document.getElementById('compressionSlider');
                const valueSpan = document.getElementById('compressionValue');
                const value = Math.round(parseFloat(slider.value) * 100);
                valueSpan.textContent = value + '%';
                
                if (dctCoefficients) {
                    reconstructImage(parseFloat(slider.value));
                }
            }
            
            // 初始化
            generateTestImage();
        </script>

        <h2>7. 深入理解：为什么DCT如此有效？</h2>

        <div class="step">
            <h3>数学直觉</h3>
            <div class="explanation">
                <p><strong>基函数的选择：</strong>DCT使用余弦函数作为基函数，这些函数在图像边界处的值为零，避免了DFT中的周期性假设问题。</p>
            </div>

            <div class="formula-box">
                <h4>DCT基函数：</h4>
                $$\phi_{u,v}(x,y) = \alpha[u]\alpha[v] \cos\left(\frac{\pi u (2x+1)}{2N}\right) \cos\left(\frac{\pi v (2y+1)}{2N}\right)$$
            </div>

            <p>每个DCT系数 $F[u,v]$ 表示图像在对应基函数上的投影强度。</p>
        </div>

        <div class="step">
            <h3>能量压缩特性</h3>
            <div class="explanation">
                <p><strong>Parseval定理：</strong>变换前后的总能量保持不变：</p>
            </div>

            <div class="formula-box">
                $$\sum_{x=0}^{N-1} \sum_{y=0}^{N-1} |f[x,y]|^2 = \sum_{u=0}^{N-1} \sum_{v=0}^{N-1} |F[u,v]|^2$$
            </div>

            <p>但DCT将能量集中到少数几个低频系数中，这就是压缩的基础。</p>
        </div>

        <h2>8. 实践建议</h2>

        <div class="step">
            <h3>选择合适的变换</h3>
            <ul>
                <li><strong>音频处理：</strong>使用DFT/FFT分析频谱</li>
                <li><strong>图像压缩：</strong>使用DCT（JPEG标准）</li>
                <li><strong>视频压缩：</strong>使用DCT + 运动补偿</li>
                <li><strong>特征提取：</strong>根据应用选择合适的变换</li>
            </ul>
        </div>

        <div class="step">
            <h3>常见误区</h3>
            <div class="explanation">
                <ul>
                    <li><strong>误区1：</strong>认为DCT总是比DFT好 → 实际上各有适用场景</li>
                    <li><strong>误区2：</strong>压缩比越高越好 → 需要平衡压缩比和质量</li>
                    <li><strong>误区3：</strong>只关注数学公式 → 理解物理意义更重要</li>
                </ul>
            </div>
        </div>

        <h2>8. 数学推导总结</h2>

        <div class="step">
            <h3>推导链条回顾</h3>
            <div class="formula-box">
                <h4>1. 傅里叶级数 → 傅里叶变换</h4>
                $$\sum_{n=-\infty}^{\infty} c_n e^{j\omega_n t} \xrightarrow{T \to \infty} \frac{1}{2\pi} \int_{-\infty}^{\infty} F(\omega) e^{j\omega t} d\omega$$

                <h4>2. 连续 → 离散</h4>
                $$\int_{-\infty}^{\infty} f(t) e^{-j\omega t} dt \xrightarrow{\text{采样}} \sum_{n=0}^{N-1} x[n] e^{-j\frac{2\pi kn}{N}}$$

                <h4>3. DFT → DCT</h4>
                $$\sum_{n=0}^{N-1} x[n] W_N^{kn} \xrightarrow{\text{对称扩展}} \sum_{n=0}^{N-1} x[n] \cos\left(\frac{\pi k (2n+1)}{2N}\right)$$
            </div>
        </div>

        <div class="step">
            <h3>关键数学概念</h3>
            <div class="explanation">
                <h4>1. 正交性原理</h4>
                <p>所有变换都基于正交基函数的展开，保证了变换的可逆性和能量守恒。</p>

                <h4>2. 采样定理</h4>
                <p>Nyquist定理：$f_s \geq 2f_{max}$ 保证了离散化的无损性。</p>

                <h4>3. 对称性利用</h4>
                <p>DCT通过对称扩展避免边界不连续，提高了压缩效率。</p>

                <h4>4. 能量集中性</h4>
                <p>自然信号的能量主要集中在低频，这是压缩的数学基础。</p>
            </div>
        </div>

        <div class="step">
            <h3>误差分析要点</h3>
            <div class="formula-box">
                <h4>量化误差：</h4>
                $$\|f - \hat{f}\|^2 = \sum_{u,v} |e_q[u,v]|^2 \leq \sum_{u,v} \frac{Q^2[u,v]}{4}$$

                <h4>截断误差：</h4>
                $$E_K = \sum_{(u,v) \notin S_K} |F[u,v]|^2$$

                <h4>总误差：</h4>
                $$E_{total} = E_{quantization} + E_{truncation}$$
            </div>
        </div>

        <div class="step">
            <h3>计算复杂度分析</h3>
            <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                <tr style="background-color: #3498db; color: white;">
                    <th style="border: 1px solid #ddd; padding: 12px;">算法</th>
                    <th style="border: 1px solid #ddd; padding: 12px;">直接计算</th>
                    <th style="border: 1px solid #ddd; padding: 12px;">快速算法</th>
                    <th style="border: 1px solid #ddd; padding: 12px;">内存需求</th>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 12px;">DFT</td>
                    <td style="border: 1px solid #ddd; padding: 12px;">$O(N^2)$</td>
                    <td style="border: 1px solid #ddd; padding: 12px;">$O(N\log N)$ (FFT)</td>
                    <td style="border: 1px solid #ddd; padding: 12px;">$O(N)$</td>
                </tr>
                <tr style="background-color: #f8f9fa;">
                    <td style="border: 1px solid #ddd; padding: 12px;">DCT</td>
                    <td style="border: 1px solid #ddd; padding: 12px;">$O(N^2)$</td>
                    <td style="border: 1px solid #ddd; padding: 12px;">$O(N\log N)$ (Fast DCT)</td>
                    <td style="border: 1px solid #ddd; padding: 12px;">$O(N)$</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 12px;">2D DCT</td>
                    <td style="border: 1px solid #ddd; padding: 12px;">$O(N^4)$</td>
                    <td style="border: 1px solid #ddd; padding: 12px;">$O(N^2\log N)$</td>
                    <td style="border: 1px solid #ddd; padding: 12px;">$O(N^2)$</td>
                </tr>
            </table>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background-color: #e8f6f3; border-radius: 10px;">
            <h3 style="color: #27ae60;">🎉 恭喜！你已经掌握了傅里叶变换家族的核心概念！</h3>
            <p>现在你可以：</p>
            <ul style="text-align: left; display: inline-block;">
                <li>理解音频和图像处理的数学基础</li>
                <li>分析JPEG等压缩算法的工作原理</li>
                <li>为进一步学习信号处理打下坚实基础</li>
            </ul>
        </div>
    </div>
</body>
</html>
