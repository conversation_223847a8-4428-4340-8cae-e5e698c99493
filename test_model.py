#!/usr/bin/env python3
"""
测试训练好的vHeat模型在验证集上的表现
输出Top-1和Top-5准确率

使用方法:
python test_model.py --data-path /root/lanyun-fs/imagenet100-split --checkpoint ./output1/vHeat_tiny_imagenet100/default/best.pth --model-size tiny
"""


import os
import sys
import argparse
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torchvision import datasets, transforms
from timm.data.constants import IMAGENET_DEFAULT_MEAN, IMAGENET_DEFAULT_STD
from timm.utils import accuracy, AverageMeter
import time

# 添加classification目录到路径
sys.path.append('classification')
from models import build_model
from utils.config import get_config

def build_transform(img_size=224):
    """构建验证集的数据变换"""
    transform = transforms.Compose([
        transforms.Resize(int((256 / 224) * img_size)),
        transforms.CenterCrop(img_size),
        transforms.ToTensor(),
        transforms.Normalize(IMAGENET_DEFAULT_MEAN, IMAGENET_DEFAULT_STD)
    ])
    return transform

def build_dataset(data_path, img_size=224):
    """构建验证数据集"""
    val_path = os.path.join(data_path, 'val')
    if not os.path.exists(val_path):
        raise ValueError(f"Validation directory does not exist: {val_path}")
    
    transform = build_transform(img_size)
    dataset = datasets.ImageFolder(val_path, transform=transform)
    
    num_classes = len(dataset.classes)
    print(f"Found {num_classes} classes in validation set")
    print(f"Total validation samples: {len(dataset)}")
    
    return dataset, num_classes

def load_model(checkpoint_path, model_size='tiny', num_classes=100, img_size=224):
    """加载训练好的模型"""
    # 创建一个临时配置
    class TempConfig:
        def __init__(self):
            self.MODEL = type('obj', (object,), {})()
            self.MODEL.TYPE = 'vHeat'
            self.MODEL.NUM_CLASSES = num_classes
            self.MODEL.DROP_PATH_RATE = 0.1 if model_size == 'tiny' else (0.3 if model_size == 'small' else 0.5)
            self.MODEL.VHEAT = type('obj', (object,), {})()
            
            if model_size == 'tiny':
                self.MODEL.VHEAT.EMBED_DIM = 96
                self.MODEL.VHEAT.DEPTHS = [2, 2, 6, 2]
            elif model_size == 'small':
                self.MODEL.VHEAT.EMBED_DIM = 96
                self.MODEL.VHEAT.DEPTHS = [2, 2, 18, 2]
            else:  # base
                self.MODEL.VHEAT.EMBED_DIM = 128
                self.MODEL.VHEAT.DEPTHS = [2, 2, 18, 2]
            
            self.MODEL.VHEAT.IN_CHANS = 3
            self.MODEL.VHEAT.PATCH_SIZE = 4
            self.MODEL.VHEAT.MLP_RATIO = 4.0
            self.MODEL.VHEAT.PATCH_NORM = True
            self.MODEL.VHEAT.POST_NORM = False if model_size == 'tiny' else True
            self.MODEL.VHEAT.LAYER_SCALE = None if model_size == 'tiny' else 1e-5
            
            self.DATA = type('obj', (object,), {})()
            self.DATA.IMG_SIZE = img_size
            self.EVAL_MODE = True
            self.THROUGHPUT_MODE = False
    
    config = TempConfig()
    
    # 构建模型
    model = build_model(config)
    
    # 加载检查点
    print(f"Loading checkpoint from: {checkpoint_path}")
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # 检查检查点中的键
    if 'model' in checkpoint:
        state_dict = checkpoint['model']
    elif 'model_ema' in checkpoint:
        state_dict = checkpoint['model_ema']
        print("Using EMA model weights")
    else:
        state_dict = checkpoint
    
    # 加载权重
    msg = model.load_state_dict(state_dict, strict=False)
    print(f"Model loaded: {msg}")
    
    if 'epoch' in checkpoint:
        print(f"Checkpoint epoch: {checkpoint['epoch']}")
    if 'max_accuracy' in checkpoint:
        print(f"Best accuracy in checkpoint: {checkpoint['max_accuracy']:.2f}%")
    
    return model

@torch.no_grad()
def validate_model(model, data_loader, device):
    """验证模型性能"""
    model.eval()
    
    batch_time = AverageMeter()
    acc1_meter = AverageMeter()
    acc5_meter = AverageMeter()
    
    end = time.time()
    
    print("Starting validation...")
    for i, (images, targets) in enumerate(data_loader):
        images = images.to(device, non_blocking=True)
        targets = targets.to(device, non_blocking=True)
        
        # 前向传播
        with torch.cuda.amp.autocast():
            outputs = model(images)
        
        # 计算准确率
        acc1, acc5 = accuracy(outputs, targets, topk=(1, 5))
        
        batch_size = targets.size(0)
        acc1_meter.update(acc1.item(), batch_size)
        acc5_meter.update(acc5.item(), batch_size)
        
        # 计算时间
        batch_time.update(time.time() - end)
        end = time.time()
        
        # 打印进度
        if i % 50 == 0:
            print(f'Test: [{i}/{len(data_loader)}]\t'
                  f'Time {batch_time.val:.3f} ({batch_time.avg:.3f})\t'
                  f'Acc@1 {acc1_meter.val:.3f} ({acc1_meter.avg:.3f})\t'
                  f'Acc@5 {acc5_meter.val:.3f} ({acc5_meter.avg:.3f})')
    
    print(f'\n=== Final Results ===')
    print(f'Top-1 Accuracy: {acc1_meter.avg:.3f}%')
    print(f'Top-5 Accuracy: {acc5_meter.avg:.3f}%')
    print(f'Average inference time: {batch_time.avg:.3f}s per batch')
    
    return acc1_meter.avg, acc5_meter.avg

def main():
    parser = argparse.ArgumentParser(description='Test vHeat model on validation set')
    parser.add_argument('--data-path', type=str, required=True,
                        help='Path to ImageNet100 dataset')
    parser.add_argument('--checkpoint', type=str, required=True,
                        help='Path to model checkpoint (.pth file)')
    parser.add_argument('--model-size', type=str, default='tiny',
                        choices=['tiny', 'small', 'base'],
                        help='Model size (default: tiny)')
    parser.add_argument('--batch-size', type=int, default=64,
                        help='Batch size for testing (default: 64)')
    parser.add_argument('--img-size', type=int, default=224,
                        help='Input image size (default: 224)')
    parser.add_argument('--num-workers', type=int, default=4,
                        help='Number of data loading workers (default: 4)')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.data_path):
        raise ValueError(f"Dataset path does not exist: {args.data_path}")
    
    if not os.path.exists(args.checkpoint):
        raise ValueError(f"Checkpoint file does not exist: {args.checkpoint}")
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 构建数据集
    dataset, num_classes = build_dataset(args.data_path, args.img_size)
    
    # 构建数据加载器
    data_loader = DataLoader(
        dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=args.num_workers,
        pin_memory=True
    )
    
    # 加载模型
    model = load_model(args.checkpoint, args.model_size, num_classes, args.img_size)
    model = model.to(device)
    
    # 计算模型参数量
    n_parameters = sum(p.numel() for p in model.parameters())
    print(f"Model parameters: {n_parameters:,}")
    
    # 验证模型
    acc1, acc5 = validate_model(model, data_loader, device)
    
    print(f"\n=== Summary ===")
    print(f"Model: vHeat-{args.model_size}")
    print(f"Dataset: {args.data_path}")
    print(f"Checkpoint: {args.checkpoint}")
    print(f"Validation samples: {len(dataset)}")
    print(f"Number of classes: {num_classes}")
    print(f"Top-1 Accuracy: {acc1:.3f}%")
    print(f"Top-5 Accuracy: {acc5:.3f}%")

if __name__ == '__main__':
    main()
