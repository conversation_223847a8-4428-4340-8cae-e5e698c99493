#!/usr/bin/env python3
"""
调试模型问题的简化脚本
"""

import os
import sys
import torch
import torch.nn.functional as F
from torchvision import datasets, transforms
from timm.data.constants import IMAGENET_DEFAULT_MEAN, IMAGENET_DEFAULT_STD

# 添加classification目录到路径
sys.path.append('classification')
from models import build_model
from utils.config import get_config

def simple_test():
    # 数据路径
    data_path = "/root/lanyun-fs/imagenet100-split"
    checkpoint_path = "./output1/vHeat_tiny_imagenet100/default/ckpt_epoch_best.pth"
    
    print("=== 1. 检查数据集 ===")
    val_path = os.path.join(data_path, 'val')
    
    # 简单的transform
    transform = transforms.Compose([
        transforms.Resize(256),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        transforms.Normalize(IMAGENET_DEFAULT_MEAN, IMAGENET_DEFAULT_STD)
    ])
    
    dataset = datasets.ImageFolder(val_path, transform=transform)
    print(f"Classes: {len(dataset.classes)}")
    print(f"Samples: {len(dataset)}")
    print(f"First few classes: {dataset.classes[:5]}")
    
    # 获取一个样本
    image, label = dataset[0]
    print(f"Sample shape: {image.shape}")
    print(f"Sample label: {label}")
    print(f"Image range: [{image.min():.3f}, {image.max():.3f}]")
    
    print("\n=== 2. 检查检查点 ===")
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    print(f"Checkpoint keys: {list(checkpoint.keys())}")
    if 'epoch' in checkpoint:
        print(f"Epoch: {checkpoint['epoch']}")
    if 'max_accuracy' in checkpoint:
        print(f"Max accuracy: {checkpoint['max_accuracy']}")
    
    print("\n=== 3. 构建模型 ===")
    # 创建临时配置
    class TempConfig:
        def __init__(self):
            self.MODEL = type('obj', (object,), {})()
            self.MODEL.TYPE = 'vHeat'
            self.MODEL.NUM_CLASSES = len(dataset.classes)
            self.MODEL.DROP_PATH_RATE = 0.1
            self.MODEL.VHEAT = type('obj', (object,), {})()
            self.MODEL.VHEAT.EMBED_DIM = 96
            self.MODEL.VHEAT.DEPTHS = [2, 2, 6, 2]
            self.MODEL.VHEAT.IN_CHANS = 3
            self.MODEL.VHEAT.PATCH_SIZE = 4
            self.MODEL.VHEAT.MLP_RATIO = 4.0
            self.MODEL.VHEAT.PATCH_NORM = True
            self.MODEL.VHEAT.POST_NORM = False
            self.MODEL.VHEAT.LAYER_SCALE = None
            
            self.DATA = type('obj', (object,), {})()
            self.DATA.IMG_SIZE = 224
            self.EVAL_MODE = True
            self.THROUGHPUT_MODE = False
    
    config = TempConfig()
    model = build_model(config)
    
    print(f"Model created, num_classes: {model.head.out_features}")
    
    print("\n=== 4. 加载权重 ===")
    if 'model' in checkpoint:
        state_dict = checkpoint['model']
    else:
        state_dict = checkpoint
    
    # 检查权重形状
    if 'head.weight' in state_dict:
        head_weight_shape = state_dict['head.weight'].shape
        print(f"Checkpoint head weight shape: {head_weight_shape}")
        print(f"Model head weight shape: {model.head.weight.shape}")
        
        if head_weight_shape[0] != model.head.weight.shape[0]:
            print(f"WARNING: Class number mismatch!")
            print(f"Checkpoint classes: {head_weight_shape[0]}")
            print(f"Dataset classes: {len(dataset.classes)}")
    
    msg = model.load_state_dict(state_dict, strict=False)
    print(f"Load result: {msg}")
    
    # 初始化推理
    if hasattr(model, 'infer_init'):
        model.infer_init()
        print("Inference initialized")
    
    print("\n=== 5. 测试单个样本 ===")
    model.eval()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # 测试单个样本
    with torch.no_grad():
        image_batch = image.unsqueeze(0).to(device)
        output = model(image_batch)
        
        print(f"Output shape: {output.shape}")
        print(f"Output range: [{output.min():.3f}, {output.max():.3f}]")
        
        # 获取预测
        probs = F.softmax(output, dim=1)
        top5_probs, top5_indices = probs.topk(5)
        
        print(f"True label: {label}")
        print(f"Top-5 predictions: {top5_indices[0].cpu().tolist()}")
        print(f"Top-5 probabilities: {top5_probs[0].cpu().tolist()}")
        
        # 检查是否预测正确
        predicted = output.argmax(dim=1).item()
        print(f"Predicted class: {predicted}")
        print(f"Correct: {predicted == label}")
        
        # 检查真实标签的概率
        true_prob = probs[0, label].item()
        print(f"True label probability: {true_prob:.6f}")
        print(f"True label rank: {(probs[0] > probs[0, label]).sum().item() + 1}")

if __name__ == '__main__':
    simple_test()
