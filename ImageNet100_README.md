# vHeat在ImageNet100数据集上的使用指南

本指南详细说明如何在ImageNet100数据集上训练和评估vHeat分类模型。

## 环境准备

### 1. 安装依赖项

```bash
pip install -r requirements.txt
```

### 2. 数据集准备

确保你的ImageNet100数据集按照以下结构组织：

```
your_imagenet100_path/
├── train/
│   ├── class1/
│   │   ├── img1.jpg
│   │   ├── img2.jpg
│   │   └── ...
│   ├── class2/
│   │   ├── img1.jpg
│   │   └── ...
│   └── ... (总共100个类别文件夹)
└── val/
    ├── class1/
    │   ├── img1.jpg
    │   └── ...
    ├── class2/
    └── ... (总共100个类别文件夹)
```

## 训练模型

### 基本训练命令

```bash
# 使用tiny模型训练
python train_imagenet100.py --data-path /root/lanyun-fs/imagenet100-split --model-size tiny --batch-size 128 --output ./output

# 使用small模型训练
python train_imagenet100.py --data-path /path/to/your/imagenet100 --model-size small --batch-size 32 --output ./output

# 使用base模型训练
python train_imagenet100.py --data-path /path/to/your/imagenet100 --model-size base --batch-size 16 --output ./output
```

### 训练参数说明

- `--data-path`: ImageNet100数据集路径
- `--model-size`: 模型大小 (tiny/small/base)
- `--batch-size`: 每个GPU的批次大小
- `--output`: 输出目录
- `--gpus`: 使用的GPU数量 (默认: 1)
- `--epochs`: 训练轮数 (默认: 300)
- `--resume`: 从检查点恢复训练
- `--pretrained`: 使用预训练权重

### 多GPU训练

```bash
# 使用4个GPU训练
python train_imagenet100.py --data-path /path/to/your/imagenet100 --model-size tiny --batch-size 32 --gpus 4 --output ./output
```

### 从检查点恢复训练

```bash
python train_imagenet100.py --data-path /path/to/your/imagenet100 --model-size tiny --resume ./output/vHeat_tiny_imagenet100/default/ckpt_epoch_100.pth
```

## 评估模型

### 基本评估命令

```bash
# 评估训练好的模型
python eval_imagenet100.py --data-path /path/to/your/imagenet100 --resume /path/to/checkpoint.pth --model-size tiny
```

### 评估参数说明

- `--data-path`: ImageNet100数据集路径
- `--resume`: 检查点文件路径
- `--model-size`: 模型大小 (tiny/small/base)
- `--batch-size`: 批次大小 (默认: 64)
- `--gpus`: 使用的GPU数量 (默认: 1)

## 直接使用原始命令

如果你想直接使用原始的训练命令，可以参考以下示例：

### 训练

```bash
python -m torch.distributed.launch --nnodes=1 --node_rank=0 --nproc_per_node=1 --master_addr="127.0.0.1" --master_port=29501 classification/main.py --cfg classification/configs/vHeat/vHeat_tiny_imagenet100.yaml --batch-size 64 --data-path /path/to/your/imagenet100 --output ./output --opts MODEL.NUM_CLASSES 100
```

### 评估

```bash
python -m torch.distributed.launch --nnodes=1 --node_rank=0 --nproc_per_node=1 --master_addr="127.0.0.1" --master_port=29501 classification/main.py --cfg classification/configs/vHeat/vHeat_tiny_imagenet100.yaml --batch-size 64 --data-path /path/to/your/imagenet100 --output ./eval_output --resume /path/to/checkpoint.pth --eval --model_ema False --opts MODEL.NUM_CLASSES 100
```

## 配置文件说明

项目包含以下ImageNet100专用配置文件：

- `classification/configs/vHeat/vHeat_tiny_imagenet100.yaml`: Tiny模型配置
- `classification/configs/vHeat/vHeat_small_imagenet100.yaml`: Small模型配置  
- `classification/configs/vHeat/vHeat_base_imagenet100.yaml`: Base模型配置

这些配置文件已经预设了100个类别，你也可以根据需要修改其他参数。

## 注意事项

1. **GPU内存**: 根据你的GPU内存调整batch_size
   - Tiny模型: 建议batch_size 64-128
   - Small模型: 建议batch_size 32-64
   - Base模型: 建议batch_size 16-32

2. **训练时间**: ImageNet100的训练时间比ImageNet-1K短很多，但仍需要几个小时到几天，取决于硬件配置

3. **数据集验证**: 脚本会自动检测数据集中的类别数，确保train和val目录中都有100个类别文件夹

4. **检查点保存**: 模型检查点会保存在输出目录中，建议定期备份重要的检查点

## 故障排除

1. **CUDA内存不足**: 减小batch_size或使用更小的模型
2. **数据集路径错误**: 确保数据集路径正确且包含train和val目录
3. **类别数不匹配**: 确保配置文件中的NUM_CLASSES与实际数据集类别数一致
